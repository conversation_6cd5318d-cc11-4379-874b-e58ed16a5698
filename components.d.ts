/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Assistant: typeof import('./src/components/common/chat/components/message/assistant.vue')['default']
    Avatar: typeof import('./src/components/common/chat/components/message/components/avatar.vue')['default']
    BottomAction: typeof import('./src/components/common/bottom-action.vue')['default']
    BottomActionArea: typeof import('./src/components/common/chat/components/input/bottom-action-area.vue')['default']
    ButtonAction: typeof import('./src/components/common/chat/components/button-action/button-action.vue')['default']
    CellPlaceholder: typeof import('./src/components/common/cell-placeholder.vue')['default']
    Chat: typeof import('./src/components/common/chat/chat.vue')['default']
    Content: typeof import('./src/components/common/chat/components/content/content.vue')['default']
    Detail: typeof import('./src/components/common/chat/components/message/components/card/detail.vue')['default']
    Footer: typeof import('./src/components/common/chat/components/footer/footer.vue')['default']
    Input: typeof import('./src/components/common/chat/components/input/input.vue')['default']
    InputActionExpand: typeof import('./src/components/common/chat/components/input/input-action-expand.vue')['default']
    Loading: typeof import('./src/components/common/chat/components/message/components/loading.vue')['default']
    Mdx: typeof import('./src/components/common/chat/components/message/components/mdx.vue')['default']
    MdxRenderer: typeof import('./src/components/common/chat/components/mdx-renderer/mdx-renderer.vue')['default']
    Message: typeof import('./src/components/common/chat/components/message/message.vue')['default']
    Messages: typeof import('./src/components/common/chat/components/messages/messages.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Speech: typeof import('./src/components/common/chat/components/speech/speech.vue')['default']
    Text: typeof import('./src/components/common/chat/components/message/components/text.vue')['default']
    Textarea: typeof import('./src/components/common/chat/components/textarea/textarea.vue')['default']
    ToastError: typeof import('./src/components/common/toast-error.vue')['default']
    ToastLoading: typeof import('./src/components/common/toast-loading.vue')['default']
    ToastSuccess: typeof import('./src/components/common/toast-success.vue')['default']
    TransitionFade: typeof import('./src/components/common/transition-fade.vue')['default']
    User: typeof import('./src/components/common/chat/components/message/user.vue')['default']
    VanPopup: typeof import('vant/es')['Popup']
    VoiceStatus: typeof import('./src/components/common/chat/components/speech/voice-status.vue')['default']
    VoiceWaveform: typeof import('./src/components/common/chat/components/speech/voice-waveform.vue')['default']
    Welcome: typeof import('./src/components/common/chat/components/welcome/welcome.vue')['default']
    WmLoading: typeof import('./src/components/common/wm-loading.vue')['default']
    WmLoadingIcon: typeof import('./src/components/common/wm-loading-icon.vue')['default']
  }
}
