import { RoleType } from '@/consts/role-type';
import AssistantMessage from './assistant.vue';
import UserMessage from './user.vue';

export function messageFactory(message: Chat.Message): typeof AssistantMessage | typeof UserMessage | null {
  switch (message.role) {
    case RoleType.ASSISTANT:
      return AssistantMessage;

    case RoleType.USER:
      return UserMessage;

    default:
      return null;
  }
}
