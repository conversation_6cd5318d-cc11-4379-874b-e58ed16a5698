<script setup lang="ts">
import { watch } from 'vue';

import Loading from './components/loading.vue';

import { RoleType } from '@/consts/role-type';
import { MessageStatus } from '@/consts/message-status';
import { Align } from '@/consts/align';


const props = defineProps<{
  message: Chat.Message;
  align?: Align;
}>();

const emits = defineEmits<{
  'on-content-change': [content: string];
}>();

function isAlignRight(message: Chat.Message) {
  if (message.align) {
    return message.align === Align.RIGHT;
  }

  if (props.align === Align.LEFT_RIGHT) {
    return message.role === RoleType.USER;
  }

  return props.align === Align.RIGHT;
}

watch(() => props.message.content, (val) => {
  emits('on-content-change', val);
}, {
  immediate: true,
});
</script>


<template>
  <div
    class="chat-message-wrap"
    :class="{
      'chat-align-right': isAlignRight(message),
    }"
  >
    <!-- 消息内容 -->
    <div
      class="chat-message"
      :class="{
        'chat-message-user': message.role === RoleType.USER,
        'chat-message-loading': message.status === MessageStatus.LOADING,
      }"
    >
      <Loading
        v-if="message.status === MessageStatus.LOADING"
      />

      <div
        v-else
      >
        {{ message.content }}
      </div>
    </div>
  </div>
</template>


<style scoped lang="less">
.chat-message-wrap {
  position: relative;
  display: flex;
  width: 100%;
  margin-bottom: 5px;
  font-size: 15px;

  &.chat-align-right {
    flex-direction: row-reverse;
  }

  &:not(:first-child) {
    margin-top: 16px;
  }

  .chat-message {
    width: fit-content;
    max-width: calc(100% - 71px);
    padding: 12px;
    color: #111;
    white-space: pre-wrap;
    border-radius: 12px;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -webkit-user-select: text;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -moz-user-select: text;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -ms-user-select: text;
    user-select: text;

    &.chat-message-user {
      color: #fff;
      background-color: #0086ff;
    }

    &.chat-message-loading {
      background-color: transparent;
    }
  }
}
</style>
