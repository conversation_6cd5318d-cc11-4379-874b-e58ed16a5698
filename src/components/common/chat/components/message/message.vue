<script setup lang="ts">
import { messageFactory } from './factory';
import { cardFactory } from './components/card';

import { Align } from '@/consts/align';

defineProps<{
  message: Chat.Message;
  align?: Align;
}>();
</script>


<template>
  <component
    :is="messageFactory(message)"
    :message="message"
    :align="align"
  />

  <component
    :is="cardFactory()"
    :message="message"
  />
</template>
