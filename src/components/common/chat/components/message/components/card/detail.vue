<script setup lang="ts">
/**
 * 日程卡片组件Props接口
 */
interface ScheduleCardProps {
  /** 服务类型标签 */
  serviceLabel?: string;
  /** 日程标题 */
  title?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 日期 */
  date?: string;
  /** 状态标签（如：待审批） */
  statusLabel?: string;
  /** 状态颜色 */
  statusColor?: string;
  /** 是否可点击 */
  clickable?: boolean;
}

const props = withDefaults(defineProps<ScheduleCardProps>(), {
  title: '日程',
  startTime: '10:00',
  date: '2025/01/01',
  serviceLabel: '日程',
  endTime: '2025/01/01',
  statusLabel: '2025/01/01',
  statusColor: '#FF9A2E',
  clickable: true,
});

const emits = defineEmits<{
  'on-click': [];
}>();

function handleClick() {
  if (props.clickable) {
    emits('on-click');
  }
}
</script>


<template>
  <div
    class="schedule-card"
    :class="{ 'schedule-card--clickable': clickable }"
    @click="handleClick"
  >
    <div class="schedule-card__content">
      <!-- 服务标签和状态 -->
      <div class="schedule-card__header">
        <div class="schedule-card__service">
          <span class="schedule-card__service-label">{{ serviceLabel }}</span>

          <!-- 状态标签（如果有） -->
          <div
            v-if="statusLabel"
            class="schedule-card__status"
          >
            <div
              class="schedule-card__status-dot"
              :style="{ backgroundColor: statusColor }"
            />
            <span class="schedule-card__status-label">{{ statusLabel }}</span>
          </div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="schedule-card__main">
        <!-- 标题 -->
        <div class="schedule-card__title">
          <h3 class="schedule-card__title-text">
            {{ title }}
          </h3>
        </div>

        <!-- 时间信息 -->
        <div class="schedule-card__time">
          <div class="schedule-card__time-content">
            <span class="schedule-card__time-label">時间:</span>
            <span class="schedule-card__time-value">
              {{ date }} {{ startTime }}{{ endTime ? `-${endTime}` : '' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 箭头图标 -->
    <div
      v-if="clickable"
      class="schedule-card__arrow"
    >
      <img
        src="@/assets/img/chat/icon-chevron-right.svg"
        alt="查看详情"
      >
    </div>
  </div>
</template>


<style scoped lang="less">
.schedule-card {
  position: relative;
  display: flex;
  flex: 1;
  gap: 39px;
  align-items: stretch;
  justify-content: stretch;
  padding: 12px 12px 14px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 -1px 2px 0 #0000000f, 0 2px 4px 0 #00000024;

  &--clickable {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 8px 0 #00000029, 0 -1px 2px 0 #00000014;
    }

    &:active {
      transform: translateY(1px);
    }
  }

  &__content {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 4px;
  }

  &__header {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &__service {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }

  &__service-label {
    color: #0086ff;
    font-weight: 500;
    font-size: 12px;
    font-family: 'PingFang SC', sans-serif;
    line-height: 1.4;
  }

  &__status {
    display: flex;
    gap: 4px;
    align-items: center;
    opacity: 0; // 根据Figma设计，状态标签默认隐藏
  }

  &__status-dot {
    width: 7px;
    height: 7px;
    background-color: #ff9a2e;
    border-radius: 50%;
  }

  &__status-label {
    color: #868ea1;
    font-weight: 400;
    font-size: 12px;
    font-family: 'PingFang HK', sans-serif;
    line-height: 1.3;
    text-transform: uppercase;
  }

  &__main {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__title {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &__title-text {
    margin: 0;
    color: #444;
    font-weight: 500;
    font-size: 16px;
    font-family: 'PingFang SC', sans-serif;
    line-height: 1.4;
  }

  &__time {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__time-content {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  &__time-label,
  &__time-value {
    color: #888;
    font-weight: 400;
    font-size: 12px;
    font-family: 'PingFang SC', sans-serif;
    line-height: 1.4;
  }

  &__arrow {
    position: absolute;
    top: 35.5px;
    right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
