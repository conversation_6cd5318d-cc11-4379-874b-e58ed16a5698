<script setup lang="ts">
import { watch } from 'vue';

import Avatar from './components/avatar.vue';
import Loading from './components/loading.vue';
import Mdx from './components/mdx.vue';
import Text from './components/text.vue';

import { MessageType } from '@/consts/message-type';
import { RoleType } from '@/consts/role-type';
import { MessageStatus } from '@/consts/message-status';
import { Align } from '@/consts/align';


const props = defineProps<{
  message: Chat.Message;
  align?: Align;
}>();

const emits = defineEmits<{
  'on-content-change': [content: string];
}>();

function isAlignRight(message: Chat.Message) {
  if (message.align) {
    return message.align === Align.RIGHT;
  }

  if (props.align === Align.LEFT_RIGHT) {
    return message.role === RoleType.USER;
  }

  return props.align === Align.RIGHT;
}

watch(() => props.message.content, (val) => {
  emits('on-content-change', val);
}, {
  immediate: true,
});
</script>


<template>
  <div
    class="chat-message-wrap"
    :class="{
      'chat-align-right': isAlignRight(message),
    }"
  >
    <!-- 头像 -->
    <Avatar
      class="message-avatar"
    />
    <!-- 消息内容 -->
    <div
      class="chat-message"
      :class="{
        'chat-message-printing': message.status === MessageStatus.INCOMPLETE,
        'chat-message-assistant': message.role === RoleType.ASSISTANT,
        'chat-message-loading': message.status === MessageStatus.LOADING,
      }"
    >
      <Loading
        v-if="message.status === MessageStatus.LOADING"
      />

      <template v-else>
        <Mdx
          v-if="message.type === MessageType.MDX"
          :message="message"
        />

        <Text
          v-else
          :message="message"
        />
      </template>
    </div>
  </div>
</template>


<style scoped lang="less">
@keyframes blink-caret {
  /* stylelint-disable-next-line keyframe-selector-notation */
  from, to {
    border-right-color: transparent;
  }

  50% {
    border-right-color: #111;
  }
}

@keyframes loading {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0.3;
  }
}

.blink {
  &::after {
    display: inline-block;
    width: 0;
    height: 14px;
    margin-left: 4px;
    vertical-align: middle;
    border-right: 4px solid #111;
    transform: translateY(-2px);
    animation: blink-caret 0.5s infinite;
    content: '';
  }
}

.chat-message-wrap {
  position: relative;
  display: flex;
  width: 100%;
  margin-bottom: 5px;
  font-size: 15px;

  &:not(:first-child) {
    margin-top: 16px;
  }

  &.chat-align-right {
    flex-direction: row-reverse;
  }

  .chat-message {
    width: fit-content;
    max-width: calc(100% - 71px);
    padding: 12px;
    color: #111;
    white-space: pre-wrap;
    border-radius: 12px;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -webkit-user-select: text;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -moz-user-select: text;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -ms-user-select: text;
    user-select: text;

    &.chat-message-assistant {
      background-color: #fff;
    }

    &.chat-message-loading {
      background-color: transparent;
    }

    &.chat-message-printing {
      // 只在整个消息的最后一个顶级元素上显示光标
      // 避免每个段落都显示光标

      // 对于非列表的最后一个元素
      > :deep(*:last-child:not(ul, ol)) {
        .blink();
      }

      // 对于列表，只在最后一个列表的最后一个列表项的内容上显示光标
      > :deep(ul:last-child),
      > :deep(ol:last-child) {
        > li:last-child {
          > p:last-child,
          > span:last-child,
          > div:last-child,
          > h1:last-child,
          > h2:last-child,
          > h3:last-child,
          > h4:last-child,
          > h5:last-child,
          > h6:last-child {
            .blink();
          }

          // 如果列表项没有子元素，直接在列表项上添加光标
          &:not(:has(p, span, div, h1, h2, h3, h4, h5, h6)) {
            .blink();
          }
        }
      }
    }
  }
}
</style>
